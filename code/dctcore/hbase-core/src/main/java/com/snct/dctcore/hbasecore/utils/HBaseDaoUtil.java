package com.snct.dctcore.hbasecore.utils;

import com.google.common.collect.Lists;
import com.snct.dctcore.commoncore.annotation.HBaseColumn;
import com.snct.dctcore.commoncore.annotation.HBaseTable;
import com.snct.dctcore.commoncore.utils.DateUtils;
import com.snct.dctcore.commoncore.utils.RandomUtil;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hbase.HColumnDescriptor;
import org.apache.hadoop.hbase.HTableDescriptor;
import org.apache.hadoop.hbase.NamespaceDescriptor;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.filter.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;

/**
 * @Author: tsohan
 * @Descriptions: HBaseDao操作公共类
 */
@Component("hBaseDaoUtil")
public class HBaseDaoUtil {

    protected final org.slf4j.Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 按天分区的预分区key
     */
    public static final byte[][] DAY_SPLIT_KEYS = new byte[][]{Bytes.toBytes("1"), Bytes.toBytes("2"), Bytes.toBytes("3"), Bytes.toBytes("4"),
            Bytes.toBytes("5"), Bytes.toBytes("6"), Bytes.toBytes("7"), Bytes.toBytes("8"), Bytes.toBytes("9"),
            Bytes.toBytes("10"), Bytes.toBytes("11"), Bytes.toBytes("12"), Bytes.toBytes("13"), Bytes.toBytes("14"),
            Bytes.toBytes("15"), Bytes.toBytes("16"), Bytes.toBytes("17"), Bytes.toBytes("18"), Bytes.toBytes("19"),
            Bytes.toBytes("20"), Bytes.toBytes("21"), Bytes.toBytes("22"), Bytes.toBytes("23"), Bytes.toBytes("24"),
            Bytes.toBytes("25"), Bytes.toBytes("26"), Bytes.toBytes("27"), Bytes.toBytes("28"), Bytes.toBytes("29"),
            Bytes.toBytes("30"), Bytes.toBytes("31")};

    // 关闭连接
    public static void close() {
        if (HConnectionFactory.connection != null) {
            try {
                HConnectionFactory.connection.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * @param name
     * @Descripton: 创建命名空间
     * @Author: tsohan
     */
    public void createNameSpace(String name) {
        try (Admin admin = HConnectionFactory.connection.getAdmin();) {
            // 获取一个namespace的描述器
            NamespaceDescriptor nsd = NamespaceDescriptor.create(name).build();
            admin.createNamespace(nsd);
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("创建命名空间失败！", e);
        }
    }

    /**
     * @param name
     * @Descripton: 删除命名空间
     * @Author: tsohan
     */
    public void dropNameSpace(String name) {
        try (Admin admin = HConnectionFactory.connection.getAdmin();) {
            admin.deleteNamespace(name);
        } catch (IOException e) {
            logger.error("删除命名空间失败！", e);
        }
    }

    public boolean tableExists(String tableName) {
        boolean b = false;
        TableName tn = TableName.valueOf(tableName);
        try (Admin admin = HConnectionFactory.connection.getAdmin();) {
            TableName[] tableNames = admin.listTableNames();
            for (TableName table : tableNames) {
                if (table.equals(tn)) {
                    b = true;
                }
            }
        } catch (IOException e) {
            logger.error("查询失败！--{}", e);
        }
        return b;
    }

    /**
     * @param tableName
     * @param familyColumn
     * @Descripton: 创建表
     * @Author: tsohan
     */
    public void createTable(String tableName, Set<String> familyColumn) {
        createTable(tableName, familyColumn, null);
    }

    public void createTable(Set<String> tableNames, Set<String> familyColumn, byte[][] splitKeys) {
        for (String tableName : tableNames) {
            createTable(tableName, familyColumn, splitKeys);
        }
    }

    /**
     * @param tableName
     * @param familyColumn
     * @Descripton: 创建表
     * @Author: tsohan
     */
    public void createTable(String tableName, Set<String> familyColumn, byte[][] splitKeys) {
        TableName tn = TableName.valueOf(tableName);
        try (Admin admin = HConnectionFactory.connection.getAdmin();) {
            HTableDescriptor htd = new HTableDescriptor(tn);
            for (String fc : familyColumn) {
                HColumnDescriptor hcd = new HColumnDescriptor(fc);
                htd.addFamily(hcd);
            }

            if (splitKeys != null) {
                admin.createTable(htd, splitKeys);
            } else {
                admin.createTable(htd);
            }
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("创建" + tableName + "表失败！", e);
        }
    }

    /**
     * 表重命名
     *
     * @param oldName
     * @param newName
     */
    public void tableRename(String oldName, String newName) {
        TableName oldTn = TableName.valueOf(oldName);
        TableName newTn = TableName.valueOf(newName);

        try (Admin admin = HConnectionFactory.connection.getAdmin()) {
            admin.disableTable(oldTn);

            // 创建快照
            String snapshot = RandomUtil.randomStr(6);
            admin.snapshot(snapshot, oldTn);

            //克隆到新表
            admin.cloneSnapshot(snapshot, newTn);

            // 删除快照
            admin.deleteSnapshot(snapshot);

            admin.deleteTable(oldTn);
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("重命名" + oldName + "表失败！");
        }
    }

    /**
     * @param tableName
     * @Descripton: 删除表
     * @Author: tsohan
     */
    public void dropTable(String tableName) {
        TableName tn = TableName.valueOf(tableName);
        try (Admin admin = HConnectionFactory.connection.getAdmin();) {
            admin.disableTable(tn);
            admin.deleteTable(tn);
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("删除" + tableName + "表失败！");
        }
    }

    /**
     * @param obj
     * @param param
     * @Descripton: 根据条件过滤查询
     * @Author: tsohan
     */
    public <T> List<T> queryScan(T obj, Map<String, String> param) throws Exception {
        List<T> objs = new ArrayList<T>();
        String tableName = getORMTable(obj);
        if (StringUtils.isBlank(tableName)) {
            return null;
        }
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin = HConnectionFactory.connection.getAdmin();) {
            if (!admin.isTableAvailable(TableName.valueOf(tableName))) {
                return objs;
            }
            Scan scan = new Scan();
            for (Map.Entry<String, String> entry : param.entrySet()) {
                Class<?> clazz = obj.getClass();
                Field[] fields = clazz.getDeclaredFields();
                for (Field field : fields) {
                    if (!field.isAnnotationPresent(HBaseColumn.class)) {
                        continue;
                    }
                    field.setAccessible(true);
                    HBaseColumn orm = field.getAnnotation(HBaseColumn.class);
                    String family = orm.family();
                    String qualifier = orm.qualifier();
                    if (qualifier.equals(entry.getKey())) {
                        Filter filter = new SingleColumnValueFilter(Bytes.toBytes(family), Bytes.toBytes(entry.getKey()), CompareFilter.CompareOp.EQUAL, Bytes.toBytes(entry.getValue()));
                        scan.setFilter(filter);
                    }
                }
            }
            ResultScanner scanner = table.getScanner(scan);
            for (Result result : scanner) {
                T beanClone = (T) BeanUtils.cloneBean(HBaseBeanUtil.resultToBean(result, obj));
                objs.add(beanClone);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("查询失败！");
            throw new Exception(e);
        }
        return objs;
    }

    /**
     * @param obj
     * @param rowkeys
     * @Descripton: 根据rowkey查询
     * @Author: tsohan
     */
    public <T> List<T> get(T obj, String... rowkeys) {
        List<T> objs = new ArrayList<T>();
        String tableName = getORMTable(obj);
        if (StringUtils.isBlank(tableName)) {
            return objs;
        }
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin = HConnectionFactory.connection.getAdmin();) {
            if (!admin.isTableAvailable(TableName.valueOf(tableName))) {
                return objs;
            }
            List<Result> results = getResults(tableName, rowkeys);
            if (results.isEmpty()) {
                return objs;
            }
            for (int i = 0; i < results.size(); i++) {
                T bean = null;
                Result result = results.get(i);
                if (result == null || result.isEmpty()) {
                    continue;
                }
                try {
                    bean = HBaseBeanUtil.resultToBean(result, obj);
                    objs.add(bean);
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("查询异常！", e);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return objs;
    }


    /**
     * @param objs
     * @Descripton: 保存实体对象
     * @Author: tsohan
     */
    public <T> boolean save(T... objs) {
        List<Put> puts = new ArrayList<Put>();
        String tableName = "";
        try (Admin admin = HConnectionFactory.connection.getAdmin();) {
            for (Object obj : objs) {
                if (obj == null) {
                    continue;
                }
                tableName = getORMTable(obj);
                // 表不存在，先获取family创建表
                if (!admin.isTableAvailable(TableName.valueOf(tableName))) {
                    // 获取family, 创建表
                    Class<?> clazz = obj.getClass();
                    Field[] fields = clazz.getDeclaredFields();
                    Set<String> set = new HashSet<>(10);
                    for (int i = 0; i < fields.length; i++) {
                        if (!fields[i].isAnnotationPresent(HBaseColumn.class)) {
                            continue;
                        }
                        fields[i].setAccessible(true);
                        HBaseColumn orm = fields[i].getAnnotation(HBaseColumn.class);
                        String family = orm.family();
                        if ("rowkey".equalsIgnoreCase(family)) {
                            continue;
                        }
                        set.add(family);
                    }
                    // 创建表
                    createTable(tableName, set);
                }
                Put put = HBaseBeanUtil.beanToPut(obj);
                puts.add(put);
            }
        } catch (Exception e) {
            logger.error("保存Hbase异常！--{}", e);
        }
        return savePut(puts, tableName);
    }

    /**
     * @param tableName
     * @param objs
     * @Descripton: 根据tableName保存
     * @Author: tsohan
     */
    public <T> void save(String tableName, T... objs) {
        List<Put> puts = new ArrayList<Put>();
        for (Object obj : objs) {
            if (obj == null) {
                continue;
            }
            try {
                Put put = HBaseBeanUtil.beanToPut(obj);
                puts.add(put);
            } catch (Exception e) {
                logger.error("hbaseDao保存出错---{}", e);
            }
        }
        boolean b = savePut(puts, tableName);
    }

    /**
     * @param obj
     * @param rowkeys
     * @Descripton: 删除
     * @Author: tsohan
     */
    public <T> void delete(T obj, String... rowkeys) {
        String tableName = getORMTable(obj);
        delete(tableName, rowkeys);
    }

    /**
     * @param tableName
     * @param rowkeys
     * @Descripton: 删除
     * @Author: tsohan
     */
    public <T> void delete(String tableName, String... rowkeys) {
        if (StringUtils.isBlank(tableName)) {
            return;
        }
        List<Delete> deletes = new ArrayList<Delete>();
        for (String rowkey : rowkeys) {
            if (StringUtils.isBlank(rowkey)) {
                continue;
            }
            deletes.add(new Delete(Bytes.toBytes(rowkey)));
        }
        delete(deletes, tableName);
    }


    /**
     * @param deletes
     * @param tableName
     * @Descripton: 批量删除
     * @Author: tsohan
     */
    private void delete(List<Delete> deletes, String tableName) {
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName));) {
            if (StringUtils.isBlank(tableName)) {
                logger.info("tableName为空！");
                return;
            }
            table.delete(deletes);
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("删除失败！", e);
        }
    }

    public void deleteByStartStop(String tableName, long startTime, long endTime) {
        if (StringUtils.isBlank(tableName)) {
            return;
        }
        List<Map<String, String>> rowMapList = getRowListByTime(startTime, endTime);

        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin = HConnectionFactory.connection.getAdmin()) {
            Scan scan;
            List<String> rowkeys = Lists.newArrayList();
            for (Map<String, String> rowMap : rowMapList) {
                scan = new Scan();
                scan.setStartRow(Bytes.toBytes(rowMap.get("sr")));
                scan.setStopRow(Bytes.toBytes(rowMap.get("er")));
                scan.setFilter(new KeyOnlyFilter());
                scanner = table.getScanner(scan);
                for (Result result : scanner) {
                    rowkeys.add(new String(result.getRow()));
                }
            }

            delete(tableName, rowkeys.toArray(new String[rowkeys.size()]));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("queryScanRowkey:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("queryScan:关闭流异常！", e);
                }
            }
        }
    }

    /**
     * @param tableName
     * @Descripton: 根据tableName获取列簇名称
     * @Author: tsohan
     */
    public List<String> familys(String tableName) {
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName));) {
            List<String> columns = new ArrayList<String>();
            if (table == null) {
                return columns;
            }
            HTableDescriptor tableDescriptor = table.getTableDescriptor();
            HColumnDescriptor[] columnDescriptors = tableDescriptor.getColumnFamilies();
            for (HColumnDescriptor columnDescriptor : columnDescriptors) {
                String columnName = columnDescriptor.getNameAsString();
                columns.add(columnName);
            }
            return columns;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("查询列簇名称失败！", e);
        }
        return new ArrayList<String>();
    }

    // 保存方法
    private boolean savePut(List<Put> puts, String tableName) {
        if (StringUtils.isBlank(tableName)) {
            return false;
        }
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin = HConnectionFactory.connection.getAdmin();) {
            table.put(puts);
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    // 获取tableName
    private String getORMTable(Object obj) {
        HBaseTable table = obj.getClass().getAnnotation(HBaseTable.class);
        return table.tableName();
    }

    // 获取查询结果
    private List<Result> getResults(String tableName, String... rowkeys) {
        List<Result> resultList = new ArrayList<Result>();
        List<Get> gets = new ArrayList<Get>();
        for (String rowkey : rowkeys) {
            if (StringUtils.isBlank(rowkey)) {
                continue;
            }
            Get get = new Get(Bytes.toBytes(rowkey));
            gets.add(get);
        }
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName));) {
            Result[] results = table.get(gets);
            Collections.addAll(resultList, results);
            return resultList;
        } catch (Exception e) {
            e.printStackTrace();
            return resultList;
        }
    }

    /**
     * @param obj
     * @param param
     * @Descripton: 根据条件过滤查询（大于等于）
     * @Author: tsohan
     */
    public <T> List<T> queryScanGreater(T obj, Map<String, String> param) throws Exception {
        List<T> objs = new ArrayList<T>();
        String tableName = getORMTable(obj);
        if (StringUtils.isBlank(tableName)) {
            return null;
        }
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin = HConnectionFactory.connection.getAdmin();) {
            if (!admin.isTableAvailable(TableName.valueOf(tableName))) {
                return objs;
            }
            Scan scan = new Scan();
            for (Map.Entry<String, String> entry : param.entrySet()) {
                Class<?> clazz = obj.getClass();
                Field[] fields = clazz.getDeclaredFields();
                for (Field field : fields) {
                    if (!field.isAnnotationPresent(HBaseColumn.class)) {
                        continue;
                    }
                    field.setAccessible(true);
                    HBaseColumn orm = field.getAnnotation(HBaseColumn.class);
                    String family = orm.family();
                    String qualifier = orm.qualifier();
                    if (qualifier.equals(entry.getKey())) {
                        Filter filter = new SingleColumnValueFilter(Bytes.toBytes(family), Bytes.toBytes(entry.getKey()), CompareFilter.CompareOp.GREATER_OR_EQUAL, Bytes.toBytes(entry.getValue()));
                        scan.setFilter(filter);
                    }
                }
            }
            ResultScanner scanner = table.getScanner(scan);
            for (Result result : scanner) {
                T beanClone = (T) BeanUtils.cloneBean(HBaseBeanUtil.resultToBean(result, obj));
                objs.add(beanClone);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("查询失败！");
            throw new Exception(e);
        }
        return objs;
    }

    /**
     * 根据rowkey查询记录
     *
     * @param obj
     * @param rowkey
     * @param <T>
     * @return
     */
    public <T> List<T> queryScanRowkey(T obj, String rowkey) {
        List<T> objs = new ArrayList<T>();
        String tableName = getORMTable(obj);
        if (StringUtils.isBlank(tableName)) {
            return null;
        }
        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin = HConnectionFactory.connection.getAdmin()) {
            Scan scan = new Scan();
            scan.setRowPrefixFilter(Bytes.toBytes(rowkey));
            scanner = table.getScanner(scan);
            for (Result result : scanner) {
                T beanClone = (T) BeanUtils.cloneBean(HBaseBeanUtil.resultToBean(result, obj));
                objs.add(beanClone);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("queryScanRowkey:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("queryScan:关闭流异常！", e);
                }
            }
        }
        return objs;
    }

    public <T> List<T> scanRowkeyList(T obj, List<String> rowkeyList) {
        List<T> objs = new ArrayList<T>();
        String tableName = getORMTable(obj);
        if (StringUtils.isBlank(tableName)) {
            return null;
        }
        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin = HConnectionFactory.connection.getAdmin()) {
            Scan scan = new Scan();
            FilterList filterList = new FilterList(FilterList.Operator.MUST_PASS_ONE);
            RowFilter rowFilter;
            Long sT = System.currentTimeMillis();

            for (String rowkey : rowkeyList) {
                rowFilter = new RowFilter(CompareFilter.CompareOp.EQUAL, new BinaryComparator(Bytes.toBytes(rowkey)));
                filterList.addFilter(rowFilter);
            }

            scan.setFilter(filterList);
            scanner = table.getScanner(scan);

            sT = System.currentTimeMillis();
            for (Result result : scanner) {
                T beanClone = (T) BeanUtils.cloneBean(HBaseBeanUtil.resultToBean(result, obj));
                objs.add(beanClone);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("scanRowkeyList:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("scanRowkeyList:关闭流异常！", e);
                }
            }
        }
        return objs;
    }

    /**
     * 根据startRow、endRow查询
     *
     * @param obj
     * @param start startRowKey
     * @param stop  stopRowKey
     * @param <T>
     * @return
     */
    public <T> List<T> scanByStartStop(T obj, String tableName, String start, String stop) {
        List<T> objs = new ArrayList<T>();
        if (StringUtils.isBlank(tableName)) {
            tableName = getORMTable(obj);
        }
        if (StringUtils.isBlank(tableName)) {
            return null;
        }
        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin = HConnectionFactory.connection.getAdmin()) {
            Scan scan = new Scan();
            scan.setStartRow(Bytes.toBytes(start));
            scan.setStopRow(Bytes.toBytes(stop));
            scanner = table.getScanner(scan);

            for (Result result : scanner) {
                T beanClone = (T) BeanUtils.cloneBean(HBaseBeanUtil.resultToBean(result, obj));
                objs.add(beanClone);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("queryScanRowkey:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("queryScan:关闭流异常！", e);
                }
            }
        }
        return objs;
    }

    /**
     * 根据范围查询数据
     *
     * @param obj
     * @param tableName
     * @param rowList
     * @param <T>
     * @return
     */
    public <T> List<T> scanByRowList(T obj, String tableName, List<Map<String, String>> rowList) {
        List<T> objs = Lists.newArrayList();
        if (StringUtils.isBlank(tableName)) {
            return objs;
        }
        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin = HConnectionFactory.connection.getAdmin()) {
            Scan scan;
            List<Result> resultList = Lists.newArrayList();
            for (Map<String, String> rowMap : rowList) {
                scan = new Scan();
                scan.setStartRow(Bytes.toBytes(rowMap.get("sr")));
                scan.setStopRow(Bytes.toBytes(rowMap.get("er")));
                scanner = table.getScanner(scan);
                for (Result result : scanner) {
                    resultList.add(result);
                }
            }

            objs = HBaseBeanUtil.resultToBean(resultList, obj, objs);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("queryScanRowkey:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("queryScan:关闭流异常！", e);
                }
            }
        }
        return objs;
    }

    /**
     * 根据范围分页查询
     *
     * @param obj
     * @param tableName
     * @param objs
     * @param rowList
     * @param currentPage
     * @param pageSize
     * @param sort
     * @param <T>
     * @return
     */
    public <T> Integer scanByStartStop4PageDesc(T obj, String tableName, List<T> objs, List<Map<String, String>> rowList, int currentPage, int pageSize, String sort) {
        List<byte[]> rows = Lists.newArrayList();
        if (StringUtils.isBlank(tableName)) {
            return 0;
        }

        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin = HConnectionFactory.connection.getAdmin()) {
            int firstIndex = 0;
            if (currentPage > 1) {
                firstIndex = (currentPage - 1) * pageSize;
            }
            int endIndex = currentPage * pageSize;

            Scan scan;
            for (Map<String, String> rowMap : rowList) {
                scan = new Scan();
                scan.setStartRow(Bytes.toBytes(rowMap.get("sr")));
                scan.setStopRow(Bytes.toBytes(rowMap.get("er")));
                scan.setFilter(new KeyOnlyFilter());
                scan.setCaching(5000);
                scanner = table.getScanner(scan);

                int size;
                Result[] results;
                do {
                    results = scanner.next(5000);
                    size = results.length;
                    Arrays.stream(results).forEach(result -> rows.add(result.getRow()));
                } while (size == 5000);
            }
            // list倒序
            if ("descending".equals(sort)) {
                Collections.reverse(rows);
            }

            List<String> getRows = Lists.newArrayList();
            for (int i = firstIndex; i < rows.size(); i++) {
                if (i > endIndex) {
                    break;
                }
                getRows.add(new String(rows.get(i)));
            }

            List<Result> resultList = getResults(tableName, getRows.toArray(new String[getRows.size()]));
            HBaseBeanUtil.resultToBean(resultList, obj, objs);

        } catch (Exception e) {
            e.printStackTrace();
            logger.error("queryScanRowkey:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("queryScan:关闭流异常！", e);
                }
            }
        }
        return rows.size();
    }

    /**
     * 获取最新一条
     *
     * @param obj
     * @param tableName
     * @param <T>
     * @return
     */
    public <T> T getLatestRow(T obj, String tableName) {
        return getLatestRow(obj, tableName, System.currentTimeMillis());
    }

    public <T> T getLatestRow(T obj, String tableName, Long lastTime) {
        return getLatestRow(obj, tableName, lastTime, 90);
    }

    /**
     * 获取时间前最新一条
     *
     * @param obj
     * @param tableName
     * @param <T>
     * @return
     */
    public <T> T getLatestRow(T obj, String tableName, Long lastTime, Integer maxHead) {
        if (StringUtils.isBlank(tableName)) {
            return null;
        }
        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin = HConnectionFactory.connection.getAdmin()) {
            Scan scan = new Scan();

            long startTime = DateUtils.addDay(lastTime, -maxHead);
            List<Map<String, String>> rowKeyList = getRowListByTime(startTime, lastTime);

            for (Map<String, String> map : rowKeyList) {
                scan.setStartRow(Bytes.toBytes(map.get("sr")));
                scan.setStopRow(Bytes.toBytes(map.get("er")));
                scanner = table.getScanner(scan);
                Result result = scanner.next();
                if (result != null) {
                    obj = HBaseBeanUtil.resultToBean(result, obj);
                    return obj;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("queryScanRowkey:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("queryScan:关闭流异常！", e);
                }
            }
        }
        return obj;
    }

    /**
     * 获取表中第一条数据的rowKey
     *
     * @param obj
     * @param tableName
     * @param <T>
     * @return
     */
    public <T> String getFirstRowKey(T obj, String tableName) {
        if (StringUtils.isBlank(tableName)) {
            return null;
        }
        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin = HConnectionFactory.connection.getAdmin()) {
            Scan scan = new Scan();

            long endTime = System.currentTimeMillis();
            // 两年的时间长度
            long startTime = DateUtils.addDay(endTime, -(365 * 5));
            List<Map<String, String>> rowKeyList = getRowListByTime(startTime, endTime);
            Collections.reverse(rowKeyList);

            Result lastResult = null;
            int beforeLast = 0;
            for (Map<String, String> map : rowKeyList) {
                scan.setStopRow(Bytes.toBytes(map.get("er")));
                scan.setStartRow(Bytes.toBytes(map.get("sr")));
                scanner = table.getScanner(scan);
                Result result = scanner.next();
                if (result != null) {
                    lastResult = result;
                    beforeLast = 0;
                    continue;
                }
                beforeLast++;
                // 往前查半年都没有数据，则认定为第一条数据
                if (beforeLast > 180) {
                    break;
                }
            }

            if (lastResult != null) {
                return new String(lastResult.getRow());
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("queryScanRowkey:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("queryScan:关闭流异常！", e);
                }
            }
        }
        return "";
    }

    /**
     * 查询数量
     *
     * @param tableName
     * @param rowList
     * @return
     */
    public Integer queryNumByStartStop(String tableName, List<Map<String, String>> rowList) {
        Integer num = 0;
        if (StringUtils.isBlank(tableName)) {
            return num;
        }
        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin = HConnectionFactory.connection.getAdmin()) {
            Scan scan;
            for (Map<String, String> rowMap : rowList) {
                scan = new Scan();
                scan.setStopRow(Bytes.toBytes(rowMap.get("er")));
                scan.setStartRow(Bytes.toBytes(rowMap.get("sr")));
                scanner = table.getScanner(scan);
                while (scanner.next() != null) {
                    num++;
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            logger.error("queryScanRowkey:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("queryScan:关闭流异常！", e);
                }
            }
        }
        return num;
    }

    /**
     * 通过时间范围获取rowKey范围数组
     *
     * @param startTime
     * @param endTime
     */
    public List<Map<String, String>> getRowListByTime(Long startTime, Long endTime) {
        List<Map<String, Long>> timeList = DateUtils.splitDate(startTime, endTime);

        List<Map<String, String>> rowList = Lists.newArrayList();
        Map<String, String> rowMap;

        for (Map<String, Long> tMap : timeList) {
            rowMap = new HashMap<>();

            rowMap.put("sr", getRowKey(tMap.get("st")));
            rowMap.put("er", getRowKey(tMap.get("et")));

            rowList.add(rowMap);
        }

        return rowList;
    }

    public String getRowKey(Long timeStamp) {
        return DateUtils.getDate(timeStamp) + "|" + DateUtils.fetchWholeSecond(timeStamp);
    }

    /**
     * 获取表名
     *
     * @param sn
     * @param typeStr
     * @param deviceCode
     * @param interval
     * @return
     */
    public String getTableName(String sn, String typeStr, String deviceCode, Integer interval) {
        if (interval == 0) {
            return sn + ":" + typeStr + "_" + deviceCode;
        }
        return sn + ":" + typeStr + "_" + deviceCode + "-" + interval;
    }

    public String getTableName(String sn, String typeStr) {
        return sn + ":" + typeStr;
    }

    public String getTableName(String typeStr, String deviceCode, Integer interval) {
        if (interval == 100) {
            return typeStr + "_" + deviceCode + "-all";
        }
        if (interval == 0) {
            return typeStr + "_" + deviceCode;
        }
        return typeStr + "_" + deviceCode + "-" + interval;
    }
}
