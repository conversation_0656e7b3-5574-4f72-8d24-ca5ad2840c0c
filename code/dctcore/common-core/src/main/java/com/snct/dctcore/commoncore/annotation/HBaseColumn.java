package com.snct.dctcore.commoncore.annotation;

import java.lang.annotation.*;

/**
 * @Author: t<PERSON>han
 * @Descriptions: 自定义注解，用于描述字段所属的 family与qualifier. 也就是hbase的列与列族
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD })
@Inherited
public @interface HBaseColumn {

    String family() default "";

    String qualifier() default "";

    boolean timestamp() default false;
}
