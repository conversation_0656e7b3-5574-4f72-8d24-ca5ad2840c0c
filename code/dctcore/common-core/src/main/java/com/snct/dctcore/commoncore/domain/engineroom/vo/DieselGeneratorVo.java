package com.snct.dctcore.commoncore.domain.engineroom.vo;

import com.snct.dctcore.commoncore.annotation.HBaseColumn;
import com.snct.dctcore.commoncore.annotation.HBaseTable;
import com.snct.dctcore.commoncore.domain.engineroom.EngineroomData;
import com.snct.dctcore.commoncore.utils.AnalysisUtils;

import java.util.Map;

/**
 * <AUTHOR>
 */
@HBaseTable(tableName = "ns1:diesel_generator")
public class DieselGeneratorVo {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    private Long time;

    private String bjTime;

    /**
     * 1#发电机转速  01002
     */
    @HBaseColumn(family = "i", qualifier = "speed")
    private String speed;

    /**
     * 1#发电机负荷   01001
     */
    @HBaseColumn(family = "i", qualifier = "load")
    private String load;

    /**
     * 1#发电机透平增压器（左）排气温度  01046
     */
    @HBaseColumn(family = "i", qualifier = "meg_t_l")
    private String megTempL;

    /**
     * 1#发电机透平增压器（右）排气温度  01047
     */
    @HBaseColumn(family = "i", qualifier = "meg_t_r")
    private String megTempR;

    /**
     * 1#发电机冷却水出口温度  01042
     */
    @HBaseColumn(family = "i", qualifier = "cwo_t")
    private String cwoTemp;

    /**
     * 1#发电机空冷器出口温度   01039
     */
    @HBaseColumn(family = "i", qualifier = "actcs")
    private String actcs;

    /**
     * 1#发电机空冷器进口温度   01040
     */
    @HBaseColumn(family = "i", qualifier = "caths")
    private String caths;

    /**
     * 1#发电机启动空气压力  01013
     */
    @HBaseColumn(family = "i", qualifier = "air_p")
    private String airPressure;

    /**
     * 1#发电机燃油进口压力  01014M
     */
    @HBaseColumn(family = "i", qualifier = "foi_p")
    private String foiPress;

    /**
     * 1#发电机燃油消耗率  01003
     */
    @HBaseColumn(family = "i", qualifier = "fuel_r")
    private String fuelRate;

    /**
     * 1#发电机蓄电池电压  01004
     */
    @HBaseColumn(family = "i", qualifier = "bv")
    private String bv;

    /**
     * 1#发电机滑油进口压力 01010M
     */
    @HBaseColumn(family = "i", qualifier = "inlet_p")
    private String inletPress;

    /**
     * 1#发电机滑油压差   01043
     */
    @HBaseColumn(family = "i", qualifier = "d_p")
    private String diffPressure;

    /**
     * 1#发电机海水进口压力  01025M
     */
    @HBaseColumn(family = "i", qualifier = "swi_p")
    private String swiPress;

    /**
     * 1#发电机缸套冷却水进口压力 01007M
     */
    @HBaseColumn(family = "i", qualifier = "jcwip")
    private String jcwip;

    /**
     * 1#发电机缸套水出口温度  01005M
     */
    @HBaseColumn(family = "i", qualifier = "jcwot")
    private String jcwot;

    /**
     * 1#发电机冷却器后温度  01022
     */
    @HBaseColumn(family = "i", qualifier = "ac_t")
    private String acTemp;

    /**
     * 1#发电机驱动端轴承温度  01035
     */
    @HBaseColumn(family = "i", qualifier = "btde")
    private String btde;

    /**
     *1#发电机自由端轴承温度  01037
     */
    @HBaseColumn(family = "i", qualifier = "btnde")
    private String btnde;

    /**
     * 1#发电机定子绕组U 温度  01029
     */
    @HBaseColumn(family = "i", qualifier = "w_t_u")
    private String tempU;

    /**
     *  1#发电机定子绕组V 温度  01031
     */
    @HBaseColumn(family = "i", qualifier = "w_t_v")
    private String tempV;

    /**
     * 1#发电机定子绕组W 温度   01033
     */
    @HBaseColumn(family = "i", qualifier = "w_t_w")
    private String tempW;

    /**
     * 1#发电机1缸排气温度  01009.1
     */
    @HBaseColumn(family = "i", qualifier = "e_t_1")
    private String  egTemp1;

    /**
     * 1#发电机2缸排气温度  01009.2
     */
    @HBaseColumn(family = "i", qualifier = "e_t_2")
    private String  egTemp2;

    /**
     * 1#发电机3缸排气温度  01009.3
     */
    @HBaseColumn(family = "i", qualifier = "e_t_3")
    private String  egTemp3;

    /**
     * 1#发电机4缸排气温度   01009.4
     */
    @HBaseColumn(family = "i", qualifier = "e_t_4")
    private String  egTemp4;

    /**
     * 1#发电机5缸排气温度  01009.5
     */
    @HBaseColumn(family = "i", qualifier = "e_t_5")
    private String  egTemp5;

    /**
     * 1#发电机6缸排气温度   01009.6
     */
    @HBaseColumn(family = "i", qualifier = "e_t_6")
    private String  egTemp6;

    /**
     * 1#发电机7缸排气温度   01009.7
     */
    @HBaseColumn(family = "i", qualifier = "e_t_7")
    private String  egTemp7;

    /**
     * 1#发电机8缸排气温度   01009.8
     */
    @HBaseColumn(family = "i", qualifier = "e_t_8")
    private String  egTemp8;

    /**
     * 1#发电机9缸排气温度   01009.9
     */
    @HBaseColumn(family = "i", qualifier = "e_t_9")
    private String  egTemp9;

    /**
     * 1#发电机10缸排气温度   01009.10
     */
    @HBaseColumn(family = "i", qualifier = "e_t_10")
    private String  egTemp10;

    /**
     * 1#发电机11缸排气温度   01009.11
     */
    @HBaseColumn(family = "i", qualifier = "e_t_11")
    private String  egTemp11;

    /**
     * 1#发电机10缸排气温度   01009.12
     */
    @HBaseColumn(family = "i", qualifier = "e_t_12")
    private String  egTemp12;

    public DieselGeneratorVo(){}

    public DieselGeneratorVo(String sign, Map<String, EngineroomData> map){

        this.speed = AnalysisUtils.analysis(map.get(sign+"002"));
        this.load = AnalysisUtils.analysis(map.get(sign+"001"));
        this.megTempL = AnalysisUtils.analysis(map.get(sign+"046"));
        this.megTempR = AnalysisUtils.analysis(map.get(sign+"047"));
        this.cwoTemp = AnalysisUtils.analysis(map.get(sign+"042"));
        this.actcs = AnalysisUtils.analysis(map.get(sign+"039"));
        this.caths = AnalysisUtils.analysis(map.get(sign+"040"));
        this.airPressure = AnalysisUtils.analysis(map.get(sign+"013"));
        this.foiPress = AnalysisUtils.analysis(map.get(sign+"014M"));
        this.fuelRate = AnalysisUtils.analysis(map.get(sign+"003"));
        this.bv = AnalysisUtils.analysis(map.get(sign+"004"));
        this.inletPress = AnalysisUtils.analysis(map.get(sign+"010M"));
        this.diffPressure = AnalysisUtils.analysis(map.get(sign+"043"));
        this.swiPress = AnalysisUtils.analysis(map.get(sign+"025M"));
        this.jcwip = AnalysisUtils.analysis(map.get(sign+"007M"));
        this.jcwot = AnalysisUtils.analysis(map.get(sign+"005M"));
        this.acTemp = AnalysisUtils.analysis(map.get(sign+"022"));
        this.btde = AnalysisUtils.analysis(map.get(sign+"035"));
        this.btnde = AnalysisUtils.analysis(map.get(sign+"037"));
        this.tempU = AnalysisUtils.analysis(map.get(sign+"029"));
        this.tempV = AnalysisUtils.analysis(map.get(sign+"031"));
        this.tempW = AnalysisUtils.analysis(map.get(sign+"033"));
        this.egTemp1 = AnalysisUtils.analysis(map.get(sign+"009.1"));
        this.egTemp2 = AnalysisUtils.analysis(map.get(sign+"009.2"));
        this.egTemp3 = AnalysisUtils.analysis(map.get(sign+"009.3"));
        this.egTemp4 = AnalysisUtils.analysis(map.get(sign+"009.4"));
        this.egTemp5 = AnalysisUtils.analysis(map.get(sign+"009.5"));
        this.egTemp6 = AnalysisUtils.analysis(map.get(sign+"009.6"));
        this.egTemp7 = AnalysisUtils.analysis(map.get(sign+"009.7"));
        this.egTemp8 = AnalysisUtils.analysis(map.get(sign+"009.8"));
        this.egTemp9 = AnalysisUtils.analysis(map.get(sign+"009.9"));
        this.egTemp10 = AnalysisUtils.analysis(map.get(sign+"009.10"));
        this.egTemp11 = AnalysisUtils.analysis(map.get(sign+"009.11"));
        this.egTemp12 = AnalysisUtils.analysis(map.get(sign+"009.12"));

    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public String getBjTime() {
        return bjTime;
    }

    public void setBjTime(String bjTime) {
        this.bjTime = bjTime;
    }

    public String getSpeed() {
        return speed;
    }

    public void setSpeed(String speed) {
        this.speed = speed;
    }

    public String getLoad() {
        return load;
    }

    public void setLoad(String load) {
        this.load = load;
    }

    public String getMegTempL() {
        return megTempL;
    }

    public void setMegTempL(String megTempL) {
        this.megTempL = megTempL;
    }

    public String getMegTempR() {
        return megTempR;
    }

    public void setMegTempR(String megTempR) {
        this.megTempR = megTempR;
    }

    public String getCwoTemp() {
        return cwoTemp;
    }

    public void setCwoTemp(String cwoTemp) {
        this.cwoTemp = cwoTemp;
    }

    public String getActcs() {
        return actcs;
    }

    public void setActcs(String actcs) {
        this.actcs = actcs;
    }

    public String getCaths() {
        return caths;
    }

    public void setCaths(String caths) {
        this.caths = caths;
    }

    public String getAirPressure() {
        return airPressure;
    }

    public void setAirPressure(String airPressure) {
        this.airPressure = airPressure;
    }

    public String getFoiPress() {
        return foiPress;
    }

    public void setFoiPress(String foiPress) {
        this.foiPress = foiPress;
    }

    public String getFuelRate() {
        return fuelRate;
    }

    public void setFuelRate(String fuelRate) {
        this.fuelRate = fuelRate;
    }

    public String getBv() {
        return bv;
    }

    public void setBv(String bv) {
        this.bv = bv;
    }

    public String getInletPress() {
        return inletPress;
    }

    public void setInletPress(String inletPress) {
        this.inletPress = inletPress;
    }

    public String getDiffPressure() {
        return diffPressure;
    }

    public void setDiffPressure(String diffPressure) {
        this.diffPressure = diffPressure;
    }

    public String getSwiPress() {
        return swiPress;
    }

    public void setSwiPress(String swiPress) {
        this.swiPress = swiPress;
    }

    public String getJcwip() {
        return jcwip;
    }

    public void setJcwip(String jcwip) {
        this.jcwip = jcwip;
    }

    public String getJcwot() {
        return jcwot;
    }

    public void setJcwot(String jcwot) {
        this.jcwot = jcwot;
    }

    public String getAcTemp() {
        return acTemp;
    }

    public void setAcTemp(String acTemp) {
        this.acTemp = acTemp;
    }

    public String getBtde() {
        return btde;
    }

    public void setBtde(String btde) {
        this.btde = btde;
    }

    public String getBtnde() {
        return btnde;
    }

    public void setBtnde(String btnde) {
        this.btnde = btnde;
    }

    public String getTempU() {
        return tempU;
    }

    public void setTempU(String tempU) {
        this.tempU = tempU;
    }

    public String getTempV() {
        return tempV;
    }

    public void setTempV(String tempV) {
        this.tempV = tempV;
    }

    public String getTempW() {
        return tempW;
    }

    public void setTempW(String tempW) {
        this.tempW = tempW;
    }

    public String getEgTemp1() {
        return egTemp1;
    }

    public void setEgTemp1(String egTemp1) {
        this.egTemp1 = egTemp1;
    }

    public String getEgTemp2() {
        return egTemp2;
    }

    public void setEgTemp2(String egTemp2) {
        this.egTemp2 = egTemp2;
    }

    public String getEgTemp3() {
        return egTemp3;
    }

    public void setEgTemp3(String egTemp3) {
        this.egTemp3 = egTemp3;
    }

    public String getEgTemp4() {
        return egTemp4;
    }

    public void setEgTemp4(String egTemp4) {
        this.egTemp4 = egTemp4;
    }

    public String getEgTemp5() {
        return egTemp5;
    }

    public void setEgTemp5(String egTemp5) {
        this.egTemp5 = egTemp5;
    }

    public String getEgTemp6() {
        return egTemp6;
    }

    public void setEgTemp6(String egTemp6) {
        this.egTemp6 = egTemp6;
    }

    public String getEgTemp7() {
        return egTemp7;
    }

    public void setEgTemp7(String egTemp7) {
        this.egTemp7 = egTemp7;
    }

    public String getEgTemp8() {
        return egTemp8;
    }

    public void setEgTemp8(String egTemp8) {
        this.egTemp8 = egTemp8;
    }

    public String getEgTemp9() {
        return egTemp9;
    }

    public void setEgTemp9(String egTemp9) {
        this.egTemp9 = egTemp9;
    }

    public String getEgTemp10() {
        return egTemp10;
    }

    public void setEgTemp10(String egTemp10) {
        this.egTemp10 = egTemp10;
    }

    public String getEgTemp11() {
        return egTemp11;
    }

    public void setEgTemp11(String egTemp11) {
        this.egTemp11 = egTemp11;
    }

    public String getEgTemp12() {
        return egTemp12;
    }

    public void setEgTemp12(String egTemp12) {
        this.egTemp12 = egTemp12;
    }
}
