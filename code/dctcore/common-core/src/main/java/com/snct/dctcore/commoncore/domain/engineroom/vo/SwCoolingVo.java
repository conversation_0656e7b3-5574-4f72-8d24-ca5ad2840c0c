package com.snct.dctcore.commoncore.domain.engineroom.vo;

import com.snct.dctcore.commoncore.annotation.Excel;
import com.snct.dctcore.commoncore.annotation.HBaseColumn;
import com.snct.dctcore.commoncore.annotation.HBaseTable;
import com.snct.dctcore.commoncore.domain.engineroom.EngineroomData;
import com.snct.dctcore.commoncore.utils.AnalysisUtils;

import java.util.Map;

/**
 * 海水冷却系统数据
 * <AUTHOR>
 */
@HBaseTable(tableName = "ns1:sw_cool")
public class SwCoolingVo {
    @HBaseColumn(family = "rowkey",qualifier = "rowkey")
    String id;

    private Long time;

    private String bjTime;

    /**
     * 1#发电机海水压力 01025
     */
    @Excel(name="1#发电机海水压力")
    @HBaseColumn(family = "i",qualifier = "swp1")
    private String seaWaterPressure1;

    /**
     * 2#发电机海水压力 02025
     */
    @Excel(name="2#发电机海水压力")
    @HBaseColumn(family = "i",qualifier = "swp2")
    private String seaWaterPressure2;

    /**
     * 3#发电机海水压力 03025
     */
    @Excel(name="3#发电机海水压力")
    @HBaseColumn(family = "i",qualifier = "swp3")
    private String seaWaterPressure3;

    public SwCoolingVo(){

    }

    public SwCoolingVo(Map<String, EngineroomData> map){
        this.seaWaterPressure1 = AnalysisUtils.analysis(map.get("01025"));
        this.seaWaterPressure2 = AnalysisUtils.analysis(map.get("02025"));
        this.seaWaterPressure3 = AnalysisUtils.analysis(map.get("03025"));
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public String getBjTime() {
        return bjTime;
    }

    public void setBjTime(String bjTime) {
        this.bjTime = bjTime;
    }

    public String getSeaWaterPressure1() {
        return seaWaterPressure1;
    }

    public void setSeaWaterPressure1(String seaWaterPressure1) {
        this.seaWaterPressure1 = seaWaterPressure1;
    }

    public String getSeaWaterPressure2() {
        return seaWaterPressure2;
    }

    public void setSeaWaterPressure2(String seaWaterPressure2) {
        this.seaWaterPressure2 = seaWaterPressure2;
    }

    public String getSeaWaterPressure3() {
        return seaWaterPressure3;
    }

    public void setSeaWaterPressure3(String seaWaterPressure3) {
        this.seaWaterPressure3 = seaWaterPressure3;
    }
}
