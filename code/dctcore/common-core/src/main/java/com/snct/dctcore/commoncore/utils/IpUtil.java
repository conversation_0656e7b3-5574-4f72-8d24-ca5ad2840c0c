package com.snct.dctcore.commoncore.utils;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.math.BigInteger;
import java.util.*;
import java.util.regex.Pattern;


/**
 * IP工具类
 * 主要有IP范围判断，IP组合
 *
 * @author: tsohan
 */
public class IpUtil {

    private static Pattern IP_PATTERN = Pattern.compile("\\b((?!\\d\\d\\d)\\d+|1\\d\\d|2[0-4]\\d|25[0-5])\\.((?!\\d\\d\\d)\\d+|1\\d\\d|2[0-4]\\d|25[0-5])\\.((?!\\d\\d\\d)\\d+|1\\d\\d|2[0-4]\\d|25[0-5])\\.((?!\\d\\d\\d)\\d+|1\\d\\d|2[0-4]\\d|25[0-5])\\b");

    private static Pattern INTERNAL_IP_PATTERN = Pattern.compile("^(127\\.0\\.0\\.1)|(localhost)|(10\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3})|(172\\.((1[6-9])|(2\\d)|(3[01]))\\.\\d{1,3}\\.\\d{1,3})|(192\\.168\\.\\d{1,3}\\.\\d{1,3})$");

    public static long strToLong(String ip) {
        String[] segments = ip.split("\\.");
        long ipL = 0;
        for (int i = 0; i < segments.length; i++) {
            ipL += (Long.parseLong(segments[i]) << (24 - 8 * i));
        }
        return ipL;
    }


    public static String longToStr(long ip) {
        StringBuilder sb = new StringBuilder();
        sb.append((ip >> 24) & 0xff).append('.');
        sb.append((ip >> 16) & 0xff).append('.');
        sb.append((ip >> 8) & 0xff).append('.');
        sb.append(ip & 0xff);
        return sb.toString();
    }

    public static boolean isSameSegment(String segment, String ip) {
        String[] split = segment.split("/");
        long ip0 = strToLong(split[0]);
        long ip1 = strToLong(ip);
        int mask = Integer.parseInt(split[1]);
        return (ip0 >> (32 - mask)) == (ip1 >> (32 - mask));
    }

    public static long getLongByMask(Long ip, int mask) {
        return ip >> (32 - mask) << (32 - mask);
    }

    public static boolean isSameIpRange(String ipRange, String ip) {
        String[] split = ipRange.split("-");
        long ip0 = strToLong(split[0].trim());
        long ip1 = strToLong(split[1].trim());
        long ip2 = strToLong(ip);
        return ip2 >= ip0 && ip2 <= ip1;
    }

    /**
     * 判断ip范围 的起始ip是否小于结束ip
     *
     * @param ipRanges
     * @throws Exception
     */
    public static void validateIpRange(String ipRanges) throws Exception {
        if (StringUtils.isBlank(ipRanges)) {
            return;
        }
        ipRanges = ipRanges.replaceAll("\n", ";");
        if (ipRanges.contains("/")) {
            return;
        }
        String[] ipRangeGroups = ipRanges.trim().split(";");
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < ipRangeGroups.length; i++) {
            if (!ipRangeGroups[i].contains("-")) {
                throw new Exception("IP范围格式有误！");
            } else {
                String[] ipStr = ipRangeGroups[i].split("-");
                long ip0 = strToLong(ipStr[0].trim());
                long ip1 = strToLong(ipStr[1].trim());
                if (ip0 > ip1) {
                    throw new Exception("结束的IP要比起始的IP大，否则无法生成IP段!");
                }
            }
        }
    }

    /**
     * 判断ip范围 的起始ip是否小于结束ip
     *
     * @param ipRanges
     * @throws Exception
     */
    public static void checkIpRange(String ipRanges) throws Exception {
        String[] ip = ipRanges.split("-");
        if (!(validateIpPattern(ip[0]) && validateIpPattern(ip[1]))) {
            throw new Exception("IP格式错误");
        }
        long ip0 = strToLong(ip[0].trim());
        long ip1 = strToLong(ip[1].trim());
        if (ip0 > ip1) {
            throw new Exception("起始IP应小于结束IP");
        }
    }

    /**
     * 校验IP格式
     *
     * @param ip
     * @return
     */
    public static boolean validateIpPattern(String ip) {
        return IP_PATTERN.matcher(ip).matches();
    }

    /**
     * 判断是否内网IP
     *
     * @param ip
     * @return
     */
    public static boolean innerIpPattern(String ip) {
        return INTERNAL_IP_PATTERN.matcher(ip).matches();
    }


    public static double log(double value, double base) {
        return Math.log(value) / Math.log(base);
    }


    public static String maskNumToBinaryString(String maskNum) {

        String mask = "";
        for (int i = 1; i <= 32; i++) {
            if (i <= Integer.parseInt(maskNum)) {
                mask = mask + "1";
            } else {
                mask = mask + "0";
            }
            if (i % 8 == 0) {
                mask = mask + (i == 32 ? "" : ".");

            }
        }
        return mask;
    }


    /**
     * lanx
     * 掩码格式转IP范围格式
     *
     * @param CIDR 掩码格式IP  eg. ************/24
     * @return IP段       eg. ************-**************
     */
    public static String CIDRToIpRange(String CIDR) throws Exception {
        String result = "";
        String maskNumSz = "";
        String maskIp = CIDR.trim();
        String[] ipStr = maskIp.split("/");
        if (ipStr.length != 2) {
            throw new Exception(maskIp + " 掩码ip格式有误!");
        }
        String ip = ipStr[0].trim();
        if (!validateIpPattern(ip)) {
            throw new Exception(maskIp + " ip格式有误!");
        }
        String maskNum = ipStr[1].trim();
        boolean isNum = maskNum.matches("[0-9]+");
        if (!isNum) {
            throw new Exception(maskIp + " 掩码位数需为整数");
        }
        if (Integer.parseInt(maskNum) > 32) {
            throw new Exception(maskIp + " 掩码超过32位");
        }

        maskNumSz = maskNumToBinaryString(maskNum);
        String subMaskIpStr = binToIpStr(maskNumSz);

        //ip与掩码作与运算求出起始ip
        StringBuilder sb = new StringBuilder();
        sb.append((strToLong(ip) >> 24 & (strToLong(subMaskIpStr) >> 24)) & 0xff).append('.');
        sb.append((strToLong(ip) >> 16 & (strToLong(subMaskIpStr) >> 16)) & 0xff).append('.');
        sb.append((strToLong(ip) >> 8 & (strToLong(subMaskIpStr) >> 8)) & 0xff).append('.');
        if (Integer.parseInt(maskNum) < 31) {
            sb.append(((strToLong(ip) & (strToLong(subMaskIpStr))) & 0xff));
        } else {
            sb.append((strToLong(ip) & (strToLong(subMaskIpStr))) & 0xff);
        }
        result = sb.toString();
        return calculateIpRange(result, maskNum);
    }


    /**
     * yjm
     * 掩码格式转IP段
     *
     * @param maskIp 掩码IP  eg. *******/***********
     * @return IP段       eg.      *******/14
     */
    public static String MaskToSeg(String maskIp) throws Exception {
        StringBuffer result = new StringBuffer();
        String[] ipStr = maskIp.split("/");
        if (ipStr.length != 2) {
            throw new Exception(maskIp + " 输入掩码ip格式有误!");
        }
        String ip = ipStr[0].trim();
        if (!validateIpPattern(ip)) {
            throw new Exception(maskIp + " ip格式有误!");
        }
        String mask = ipStr[1].trim();
        if (!validateIpPattern(mask)) {
            throw new Exception(maskIp + " 子网掩码格式有误!");
        }
        String binaryMask = ipToBinStr(mask);
        int firstZero = -1;
        for (int i = 0; i < binaryMask.length(); i++) {
            if (binaryMask.charAt(i) == '0') {
                firstZero = i;
                break;
            }
        }
        if (firstZero != -1) {
            for (int i = firstZero + 1; i < binaryMask.length(); i++) {
                if (binaryMask.charAt(i) == '1') {
                    throw new Exception(maskIp + " 子网掩码格式有误!");
                }
            }
        }
        result.append(ip).append("/").append(IpUtil.getMaskNum(mask));
        return result.toString();
    }

    /**
     * maskIp转成IP段格式
     *
     * @param maskIp
     * @return
     */
    public static String maskIpToSeg(String maskIp) {
        StringBuffer result = new StringBuffer();
        String[] ipStr = maskIp.split("/");
        if (ipStr.length != 2) {
            return result.toString();
        }
        String ip = ipStr[0].trim();
        String maskNum = ipStr[1].trim();
        if (!validateIpPattern(ip) || !maskNum.matches("[0-9]+")
                || Integer.parseInt(maskNum) > 32 || Integer.parseInt(maskNum) < 0) {
            return result.toString();
        }
        int mask = 32 - Integer.parseInt(maskNum);
        long ipInt = strToLong(ip);
        ipInt = (ipInt >> mask) << mask;
        result.append(longToStr(ipInt)).append("/").append(maskNum);
        return result.toString();
    }

    /**
     * youjm
     * 检查是否符合ip/子网掩码规则
     *
     * @param maskIp ************/24
     * @return 不符合规则的ip/子网掩码
     * @throws Exception
     */
    public static String maskCheck(String maskIp) throws Exception {
        String result = "";
        String[] ipStr = maskIp.split("/");
        if (ipStr.length != 2) {
            throw new Exception(maskIp + " 输入掩码ip格式有误!");
        }
        String ip = ipStr[0].trim();
        if (!validateIpPattern(ip)) {
            throw new Exception(maskIp + " ip格式有误!");
        }
        String maskNum = ipStr[1].trim();
        boolean isNum = maskNum.matches("[0-9]+");
        if (!isNum) {
            throw new Exception(maskIp + " 掩码位数需为整数");
        }
        if (Integer.parseInt(maskNum) > 32) {
            throw new Exception(maskIp + " 掩码位数不能超过32");
        }
        if (Integer.parseInt(maskNum) < 0) {
            throw new Exception(maskIp + " 掩码位数需大于0");
        }
        String binIp = ipToBinStr(ip);
        for (int i = Integer.parseInt(maskNum); i < binIp.length(); i++) {
            if (binIp.charAt(i) != '0') {
                result += maskIp + ";";
                break;
            }
        }
        return result;
    }

    /**
     * 判断是否符合ip/子网掩码规则， 返回true 或false
     */
    public static boolean isIpMask(String maskIp) {
        String[] ipStr = maskIp.split("/");
        if (ipStr.length != 2) {
            return false;
        }
        String ip = ipStr[0].trim();
        String maskNum = ipStr[1].trim();
        if (!validateIpPattern(ip) || !maskNum.matches("[0-9]+")
                || Integer.parseInt(maskNum) > 32 || Integer.parseInt(maskNum) < 0) {
            return false;
        }
        String binIp = ipToBinStr(ip);
        for (int i = Integer.parseInt(maskNum); i < binIp.length(); i++) {
            if (binIp.charAt(i) != '0') {
                return false;
            }
        }
        return true;
    }


    /**
     * 十进制ip转为32位二进制ip
     *
     * @param ip return binaryIp
     */
    public static String ipToBinStr(String ip) {
        String[] ips = ip.trim().split("\\.");
        StringBuffer binStr = new StringBuffer();
        for (String s : ips) {
            String part = toBinaryString(Integer.parseInt(s));
            binStr.append(part);
        }
        return binStr.toString();
    }

    //十进制转8位二进制
    public static String toBinaryString(int i) {
        char[] digits = {'0', '1'};
        char[] buf = new char[8];
        int pos = 8;
        int mask = 1;
        do {
            buf[--pos] = digits[i & mask];
            i >>>= 1;
        } while (pos > 0);

        return new String(buf, pos, 8);
    }


    /**
     * 计算起始和结束ip地址
     *
     * @param ipRange *************/24
     * @return
     */
    public static String[] getIpRange(String ipRange) {
        String[] result = new String[]{"", ""};
        if (StringUtils.isBlank(ipRange)) {
            return result;
        }

        String[] orig = ipRange.split("/");
        if (orig.length != 2) {
            return result;
        }
        String ip = orig[0];
        String mask = orig[1];

        String startEnd = calculateIpRange(ip, mask);
        String[] startEnds = startEnd.split("-");
        if (startEnds.length == 2) {
            return startEnds;
        }
        return result;
    }

    /**
     * lanx
     *
     * @param ipFinal    ip
     * @param maskLength 掩码长度
     * @return ip段
     */
    public static String calculateIpRange(String ipFinal, String maskLength) {
        String mask = "";
        for (int i = 1; i <= 32; i++) {
            if (i <= Integer.parseInt(maskLength)) {
                mask = mask + "0";
            } else {
                mask = mask + "1";
            }
            if (i % 8 == 0) {
                mask = mask + (i == 32 ? "" : ".");

            }
        }
        String subMaskIpStr = binToIpStr(mask);
        String startIp = "";
        String endIp = "";

        StringBuilder maxSb = new StringBuilder();
        maxSb.append((strToLong(ipFinal) >> 24 | (strToLong(subMaskIpStr) >> 24)) & 0xff).append('.');
        maxSb.append((strToLong(ipFinal) >> 16 | (strToLong(subMaskIpStr) >> 16)) & 0xff).append('.');
        maxSb.append((strToLong(ipFinal) >> 8 | (strToLong(subMaskIpStr) >> 8)) & 0xff).append('.');
        if (Integer.parseInt(maskLength) < 31) {
            maxSb.append(((strToLong(ipFinal) | (strToLong(subMaskIpStr))) & 0xff));
        } else {
            maxSb.append((strToLong(ipFinal) | (strToLong(subMaskIpStr))) & 0xff);
        }

        endIp = maxSb.toString();
        startIp = ipFinal;

        return startIp + "-" + endIp;
    }

    /**
     * lanx
     * 二进制ip格式转10进制
     *
     * @param maskNumSz 二进制ip
     * @return subMaskIpStr  十进制ip
     */
    public static String binToIpStr(String maskNumSz) {
        String[] subMaskIp = maskNumSz.split("\\.");
        String subMaskIpStr = "";
        for (int i = 0; i < 4; i++) {
            BigInteger src1 = new BigInteger(subMaskIp[i], 2);//转换为BigInteger类型
            subMaskIpStr = subMaskIpStr + src1.toString() + (i == 3 ? "" : ".");//转换为10进制并输出结果

        }
        return subMaskIpStr;
    }

    /**
     * lanx
     * IP段格式转掩码格式
     *
     * @param ipStart IP段起始IP        eg. ************
     * @param ipEnd   IP段结束IP        eg. **************
     * @return 掩码格式IP                eg. ************/24
     */
    public static String cacMask(String ipStart, String ipEnd) {
        String result = "";
        int maxMaskNum = getMaskNum(ipStart);
        long add = (long) Math.pow(2, 32 - maxMaskNum);
        while ((strToLong(ipStart) + add - 1) <= strToLong(ipEnd)) {
            result = result + ipStart + "/" + maxMaskNum + ";";
            long preIpStart = strToLong(ipStart);
            ipStart = longToStr(strToLong(ipStart) + add);
            if (preIpStart >= strToLong(ipStart)) {
                return result;
            }
            maxMaskNum = getMaskNum(ipStart);
            add = (long) Math.pow(2, 32 - maxMaskNum);

        }

        result = getMaxNear(ipStart, ipEnd, maxMaskNum, result);
        return result;

    }

    /**
     * lanx
     * ip段计算掩码
     *
     * @param ipStart    开始ip
     * @param ipEnd      结束ip
     * @param maxMaskNum 掩码位数
     * @param result
     */
    public static String getMaxNear(String ipStart, String ipEnd, int maxMaskNum, String result) {
        while (32 - maxMaskNum >= 0) {
            long add1 = (long) Math.pow(2, 32 - maxMaskNum);
            if ((strToLong(ipStart) + add1 - 1) <= strToLong(ipEnd)) {
                result = result + ipStart + "/" + (maxMaskNum) + ";";
                ipStart = longToStr(strToLong(ipStart) + add1);
            } else if (32 - maxMaskNum >= 0) {
                maxMaskNum = maxMaskNum + 1;

            }
        }
        return result;
    }

    /**
     * lanx
     * 计算ip掩码位数
     *
     * @param ip
     */
    public static int getMaskNum(String ip) {
        int maxMaskNum = 0;
        for (int i = 1; i <= 32; i++) {
            if (strToLong(ip) == 0) {
                break;
            }
            if (strToLong(ip) == ((strToLong(ip) & 0xffffffff << (32 - i)))) {
                maxMaskNum = i;
                break;
            }
        }
        return maxMaskNum;
    }


    /**
     * 合并ip范围  格式包括 [*********-********* , *********/24]
     *
     * @param ipRanges
     * <AUTHOR>
     */
    public static String mergeIpRanges(String[] ipRanges) throws Exception {

        Map<String, String> noStartRepeat = new HashMap<String, String>();
        //1.     起始ip有重复的先合并
        for (String ipRange : ipRanges) {
            ipRange = ipRange.trim();
            //单个ip格式*******转为 *******-*******
            if (ipRange.equals("")) {
                continue;
            }
            if (!ipRange.contains("-") && !ipRange.contains("/")) {
                ipRange = ipRange + "-" + ipRange;
            }
            if (ipRange.contains("/")) {
                ipRange = CIDRToIpRange(ipRange);
            }
            String ipStart = ipRange.split("-")[0];
            String ipEnd = ipRange.split("-")[1];
            if (!noStartRepeat.keySet().contains(ipStart)) {
                noStartRepeat.put(ipStart, ipEnd);
            } else {
                if (strToLong(ipEnd) > strToLong(noStartRepeat.get(ipStart))) {
                    //更新ip段大的
                    noStartRepeat.put(ipStart, ipEnd);
                }
            }
        }
        List<String> list = new ArrayList<String>();
        list.addAll(noStartRepeat.keySet());
        Collections.sort(list, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                if (strToLong(o1) > strToLong(o2)) {
                    return 1;
                } else if (strToLong(o1) < strToLong(o2)) {
                    return -1;
                }
                return 0;
            }
        });

        LinkedHashMap<String, String> mapSorted = new LinkedHashMap<String, String>();
        LinkedHashMap<String, String> mapResult = new LinkedHashMap<String, String>();
        for (String ip : list) {
            mapSorted.put(ip, noStartRepeat.get(ip));
        }
        list = null;
        noStartRepeat = null;
        mergeProcess(mapSorted, mapResult);
        StringBuffer sb = new StringBuffer();
        for (String ip : mapResult.keySet()) {
            sb.append(ip).append("-").append(mapResult.get(ip)).append(";");
        }
        return sb.toString();
    }

    /**
     * 判断ip是否被包含
     *
     * @param largeSegment 范围大的ip段 （*******/24;*******/24）
     * @param smallSegment 范围小的ip段 （*******/24）
     */
    public static Boolean isContainIpSegment(String largeSegment, String smallSegment) throws Exception {
        String largeIpRange = mergeIpRanges(largeSegment.split(";"));
        String smallIpRange = mergeIpRanges(smallSegment.split(";"));

        if (StringUtils.isBlank(largeIpRange) || StringUtils.isBlank(smallIpRange)) {
            return false;
        }
        String[] largeIpRangeArray = largeIpRange.split(";");
        String[] smallIpRangeArray = smallIpRange.split(";");

        for (String sr : smallIpRangeArray) {
            Boolean result = false;
            for (String lr : largeIpRangeArray) {
                if (strToLong(sr.split("-")[0]) >= strToLong(lr.split("-")[0]) && strToLong(sr.split("-")[1]) <= strToLong(lr.split("-")[1])) {
                    result = true;
                }
            }
            if (!result) {
                return false;
            }
        }
        return true;
    }

    /**
     * ip段相减，两个ip段必须是包含关系结果才是正确的
     *
     * @param largeSegment 范围大的ip段 （*******/24;*******/24）
     * @param smallSegment 范围小的ip段 （*******/24）
     *                     返回值已转为掩码并后分号隔开
     */
    public static String ipSegmentSubtract(String largeSegment, String smallSegment) throws Exception {
        Set<String> subIpSets = new HashSet<>();

        String largeIpRange = mergeIpRanges(largeSegment.split(";"));
        String smallIpRange = mergeIpRanges(smallSegment.split(";"));

        if (StringUtils.isBlank(largeIpRange) || StringUtils.isBlank(smallIpRange)) {
            return "";
        }
        String[] largeIpRangeArray = largeIpRange.split(";");
        String[] smallIpRangeArray = smallIpRange.split(";");

        for (String lr : largeIpRangeArray) {
            Boolean isContain = false;
            for (String sr : smallIpRangeArray) {
                if (!(strToLong(sr.split("-")[0]) >= strToLong(lr.split("-")[0]) && strToLong(sr.split("-")[1]) <= strToLong(lr.split("-")[1]))) {
                    continue;
                }
                isContain = true;
                String headIps = "";
                if (strToLong(sr.split("-")[0]) != strToLong(lr.split("-")[0])) {
                    headIps = lr.split("-")[0] + "-" + sr.split("-")[0];
                }
                if (StringUtils.isNotBlank(headIps)) {
                    subIpSets.add(headIps);
                }
                String tailIps = "";
                if (strToLong(sr.split("-")[1]) != strToLong(lr.split("-")[1])) {
                    tailIps = sr.split("-")[1] + "-" + lr.split("-")[1];
                }
                if (StringUtils.isNotBlank(tailIps)) {
                    subIpSets.add(tailIps);
                }
            }
            if (!isContain) {
                subIpSets.add(lr);
            }
        }

        String ipMaskStr = "";
        if (subIpSets.size() > 0) {
            ipMaskStr = IpUtil.ipRangeHandle(StringUtils.join(subIpSets, ";"));
        }

        return ipMaskStr;
    }

    /**
     * 合并ip段
     *
     * @param mapSorted 合并ip段
     * @param mapResult 有序ip段
     * @return
     */
    public static LinkedHashMap mergeProcess(LinkedHashMap<String, String> mapSorted, LinkedHashMap<String, String> mapResult) {
        while (mapSorted.size() > 0) {
            String[] ss = new String[mapSorted.keySet().size()];
            mapSorted.keySet().toArray(ss);
            if (mapSorted.size() >= 2) {
                String ip = ss[0];
                String ipT = ss[1];
                //合并相邻及包含
                if (strToLong(mapSorted.get(ip)) + 1 >= strToLong(ipT)) {
                    if (strToLong(mapSorted.get(ip)) > strToLong(mapSorted.get(ipT))) {
                        mapSorted.remove(ipT);
                    } else {
                        mapSorted.put(ip, mapSorted.get(ipT));
                        mapSorted.remove(ipT);
                    }
                } else {
                    //移除已合并
                    mapResult.put(ip, mapSorted.get(ip));
                    mapSorted.remove(ip);
                }
            } else if (mapSorted.size() == 1) {
                String ip = ss[0];
                mapResult.put(ip, mapSorted.get(ip));
                mapSorted.remove(ip);
            }
        }
        return mapResult;
    }

    /**
     * ip范围 转为具体的ip集合 [168427778,168427779,168427780]
     *
     * @param ipStart *********
     * @param ipEnd   *********
     * @return
     */
    public static Set<Long> ipRangeToList(String ipStart, String ipEnd) {
        Set<Long> ipList = new HashSet<Long>();
        StringBuffer sb = new StringBuffer();
        ipList.add(strToLong(ipStart));
        long ipStartValue = strToLong(ipStart);
        while (ipStartValue + 1 <= strToLong(ipEnd)) {
            ipList.add(ipStartValue + 1);
            ipStartValue = ipStartValue + 1;
        }

        return ipList;
    }

    /**
     * lanx  ip段转子网掩码格式
     *
     * @param ipRanges
     * @return
     */
    public static String ipRangeHandle(String ipRanges) {
        StringBuffer resultSb = new StringBuffer();
        String[] ipRangeGroups = ipRanges.trim().split(";");
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < ipRangeGroups.length; i++) {
            if (!ipRangeGroups[i].contains("-")) {
                sb.append("IP格式有误！");
            } else {
                String[] ipStr = ipRangeGroups[i].split("-");
                sb.append(IpUtil.cacMask(ipStr[0].trim(), ipStr[1].trim()));
            }
        }
        String[] tmpResult = sb.toString().trim().split(";");

        Set<String> finalResult = new HashSet<String>();
        for (String result : tmpResult) {
            finalResult.add(result);
        }
        LinkedList<String> list = new LinkedList<String>();
        list.addAll(finalResult);
        Collections.sort(list, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                if (IpUtil.strToLong(o1.substring(0, o1.indexOf("/"))) > IpUtil.strToLong(o2.substring(0, o2.indexOf("/")))) {
                    return 1;
                } else if (IpUtil.strToLong(o1.substring(0, o1.indexOf("/"))) < IpUtil.strToLong(o2.substring(0, o2.indexOf("/")))) {
                    return -1;
                }
                return 0;
            }
        });

        for (String s : list) {
            resultSb.append(s).append(";");
        }
        return resultSb.toString();
    }

    public static String binary2numStr(String binary) {
        char[] chars = binary.toCharArray();
        StringBuffer str = new StringBuffer();
        Integer sum = 0;
        for (int i = 0; i < chars.length; i++) {
            String index = String.valueOf(chars[i]);
            sum += Integer.valueOf(index) * (int) Math.pow(2, chars.length - 1 - i);
        }
        return String.valueOf(sum);
    }

    /**
     * 将32位的二进制字符串转为ip
     *
     * @param binary 32位的二进制字符串
     * @return
     */
    public static String binary2ip(String binary) {
        StringBuffer result = new StringBuffer();
        String str1 = binary.substring(0, 8);
        String str2 = binary.substring(8, 16);
        String str3 = binary.substring(16, 24);
        String str4 = binary.substring(24, 32);
        result.append(binary2numStr(str1));
        result.append(".");
        result.append(binary2numStr(str2));
        result.append(".");
        result.append(binary2numStr(str3));
        result.append(".");
        result.append(binary2numStr(str4));
        return result.toString();
    }

    /**
     * 将数字转为固定位数的二进制，不足左边补0
     *
     * @param num
     * @param binaryLength
     * @return
     */
    public static String numberToBinary(int num, int binaryLength) {
        StringBuffer result = new StringBuffer();
        for (int n = binaryLength - 1; n >= 0; n--) {
            result.append(num >>> n & 1);
        }
        return result.toString();
    }

    /**
     * 数字掩码转换为ip格式
     *
     * @param num
     * @return
     */
    public static String maskNum2Ip(int num) {
        StringBuffer result = new StringBuffer();
        if (num > 32) {
            return "***************";
        } else if (num <= 0) {
            return "0.0.0.0";
        }
        for (int i = 0; i < 32; i++) {
            if (i < num) {
                result.append("1");
            } else {
                result.append("0");
            }
        }
        return binary2ip(result.toString());
    }

    /***
     * 将ip段转化为单个ip存储在list中
     *
     * @param ipRange 格式：***********-***********10
     * @return list contains all single ip
     */
    public static List<String> ipRangeToList(String ipRange) {
        String[] firstAndLast = ipRange.split("-");
        long min = strToLong(firstAndLast[0]);
        long max = strToLong(firstAndLast[1]);
        List<String> ips = Lists.newArrayList();
        for (long ip = min; ip <= max; ip++) {
            ips.add(longToStr(ip));
        }
        return ips;
    }


    public static boolean isIp(String IP) {//判断是否是一个IP
        IP = IP.trim();
        if (IP.matches("\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}")) {
            String s[] = IP.split("\\.");
            if (Integer.parseInt(s[0]) <= 255 && Integer.parseInt(s[1]) <= 255 && Integer.parseInt(s[2]) <= 255 && Integer.parseInt(s[3]) <= 255) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否是ip 或者 ip:port
     *
     * @param domain
     * @return
     */
    public static boolean isIpOrIpPort(String domain) {
        String pattern = "((?:(?:25[0-5]|2[0-4]\\d|((1\\d{2})|([1-9]?\\d)))\\.){3}(?:25[0-5]|2[0-4]\\d|((1\\d{2})|([1-9]?\\d)))(\\:(\\d)*)?)";
        return domain.matches(pattern);
    }


    /**
     * 判断一个IP是否在一个网段里面
     *
     * @param ip     ip地址
     * @param ipMask ip/子网掩码
     */
    public static boolean isInIpMask(String ip, String ipMask) {
        if (!isIp(ip)) {
            return false;
        }
        if (!isIpMask(ipMask)) {
            return false;
        }
        String[] ipMaskRange = getIpRange(ipMask);
        long start = strToLong(ipMaskRange[0]);
        long end = strToLong(ipMaskRange[1]);
        long ipLong = strToLong(ip);
        return start <= ipLong && ipLong <= end;
    }

    /**
     * 获取标准的ip段格式
     *
     * @param ip:***********
     * @param mask:24
     * @return ***********
     * @throws Exception
     */
    public static String getStandardIpSeg(String ip, String mask) throws Exception {
        StringBuilder result = new StringBuilder();
        if (!validateIpPattern(ip)) {
            throw new Exception(ip + " ip格式有误!");
        }
        boolean isNum = mask.matches("[0-9]+");
        if (!isNum) {
            throw new Exception(ip + "/" + mask + " 掩码位数需为整数");
        }
        Integer maskNum = Integer.parseInt(mask);
        if (maskNum > 32) {
            throw new Exception(ip + "/" + mask + " 掩码位数不能超过32");
        }
        if (maskNum < 0) {
            throw new Exception(ip + "/" + mask + " 掩码位数需大于0");
        }
        String binIp = ipToBinStr(ip);
        for (int i = 0; i < binIp.length(); i++) {
            if (i < maskNum) {
                result.append(binIp.charAt(i));
            } else {
                result.append('0');
            }
            if ((i + 1) % 8 == 0 && i != binIp.length() - 1) {
                result.append('.');
            }
        }
        return binToIpStr(result.toString());
    }

    public static void main(String[] args) throws Exception {
        String s = ipSegmentSubtract("*******/24;*******/24", "*******/32");
        System.out.println(s);
        System.out.println(isIpOrIpPort("127.0.0.1"));
    }

}
