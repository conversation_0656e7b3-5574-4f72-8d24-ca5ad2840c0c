package com.snct.dctcore.commoncore.utils;

import org.assertj.core.util.Lists;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by tsohan on 2020/5/27.
 * 日期操作工具类
 */
public class DateUtils {

    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String YYYY_MM_DD_ = "yyyy/MM/dd";

    public static final String YYYY_MM = "yyyy-MM";

    public static final String YYYY = "yyyy";
    public static final String MM = "MM";
    public static final String DATE_PATTERN_NONE = "yyyyMMdd";


    /**
     * 时间戳转换成字符串
     */
    public static String getDateToString(Long time) {
        return getDateToString(time,"yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 时间戳转换成字符串
     */
    public static String getDateToString(Long time,String format) {
        if (time == null) {
            return "";
        }
        SimpleDateFormat sf = new SimpleDateFormat(format);
        Date d = new Date(time);
        return sf.format(d);
    }


    /**
     * 获取当月1号的0点0时0分的时间
     *
     * @return
     */
    public static Date getMothFirstDay() {
        SimpleDateFormat format = new SimpleDateFormat(YYYY_MM_DD);
        Calendar cal = Calendar.getInstance();
        //设置为1号,当前日期既为本月第一天
        cal.set(Calendar.DAY_OF_MONTH, 1);
        String date = format.format(cal.getTime());
        return parseDateTime(date);
    }

    /**
     * 获取指定1号的0点0时0分的时间
     *
     * @return
     */
    public static Date getMothFirstDay(Long date) throws ParseException {
        //获取当前日期
        SimpleDateFormat format = new SimpleDateFormat(YYYY_MM_DD);
        Calendar cal = Calendar.getInstance();
        cal.setTime(format.parse(format.format(new Date(date))));
        //设置为1号,当前日期既为本月第一天
        cal.set(Calendar.DAY_OF_MONTH, 1);
        return cal.getTime();
    }

    /**
     * 获取指定最后一天
     *
     * @return
     */
    public static Date getMothLastDay(Long date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date(date));
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        return cal.getTime();
    }

    /**
     * 依据给定的日期时间字符串，获取日期和时间
     *
     * @param datetime，必须是yyyy-MM-dd HH:mm:ss格式
     * @return 日期字符串对应的日期时间
     */
    public static Date parseDateTime(String datetime) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if ((datetime == null) || (datetime.equals(""))) {
            return null;
        } else {
            try {
                return formatter.parse(datetime);
            } catch (ParseException e) {
                return parseDate(datetime, YYYY_MM_DD);
            }
        }
    }


    /**
     * 依据给定的日期字符串，获取日期，时间为00:00:00
     *
     * @param date，必须是yyyy-MM-dd格式，如2013-1-1
     * @return 日期字符串对应的日期
     */
    public static Date parseDate(String date, String formatTemp) {
        SimpleDateFormat formatter = new SimpleDateFormat(formatTemp);

        if ((date == null) || (date.equals(""))) {
            return null;
        } else {
            try {
                return formatter.parse(date);
            } catch (ParseException e) {
                return null;
            }
        }
    }


    /**
     * 依据给定的日期字符串，获取日期字符串
     *
     * @param date，必须是yyyy-MM-dd格式，如2013-1-1
     * @return 日期字符串对应的日期
     */
    public static String formatDate(Date date, String formatTemp) {
        SimpleDateFormat formatter = new SimpleDateFormat(formatTemp);

        if ((date == null) || (date.equals(""))) {
            return null;
        } else {
            return formatter.format(date);
        }
    }

    /**
     * 分析统计月份每月一号时间戳
     *
     * @param statisticMonthNum 统计月份数量：统计几个月
     * @return
     */
    public static List<Long> analysisStatisticMonths(int statisticMonthNum) throws ParseException {

        List<Long> result = new ArrayList<Long>();

        if (statisticMonthNum < 1) {
            throw new NullPointerException("月份至少为1");
        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(new SimpleDateFormat("yyyy-MM-dd").parse(new SimpleDateFormat("yyyy-MM-dd").format(new Date())));
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.add(Calendar.MONTH, -1 * (statisticMonthNum - 1));

        result.add(cal.getTime().getTime());


        for (int i = 1; i < statisticMonthNum; i++) {
            cal.add(Calendar.MONTH, 1);
            result.add(cal.getTime().getTime());
        }

        return result;
    }

    /**
     * 获取下一个月
     *
     * @param monthTime 月份时间戳
     * @return
     */
    public static Long fetchNextMonth(Long monthTime) throws ParseException {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date(monthTime));
        cal.add(Calendar.MONTH, 1);
        return cal.getTime().getTime();
    }

    /**
     * 获取上一个月
     *
     * @param monthTime 月份时间戳
     * @return
     */
    public static Long fetchPreMonth(Long monthTime) throws ParseException {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date(monthTime));
        cal.add(Calendar.MONTH, -1);
        return cal.getTime().getTime();
    }

    /**
     * 获取指定月份倒数第几天,例如：获取8月倒数第15天
     *
     * @param monthTime 月份时间戳：当月任意一天
     * @param remainDay 倒数天数
     * @return
     */
    public static Long fetchRemainDays(Long monthTime, int remainDay) throws ParseException {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date(monthTime));
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH) - remainDay + 1);

        return cal.getTime().getTime();
    }


    /**
     * 获取下一天的日期,一般用于获取24点的时间戳
     *
     * @param date
     * @return
     * @throws ParseException
     */
    public static Date fetchNextDate(Long date) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();//获取当前日期
        cal.setTime(format.parse(format.format(new Date(date))));
        cal.add(Calendar.DAY_OF_MONTH, 1);

        return cal.getTime();
    }

    /**
     * 从指定日期往前算n天
     *
     * @param date
     * @param preNum 往前几天
     * @return
     * @throws ParseException
     */
    public static Date fetchPreDate(Long date, int preNum) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();//获取当前日期
        cal.setTime(format.parse(format.format(new Date(date))));
        cal.add(Calendar.DAY_OF_MONTH, -1 * preNum);

        return cal.getTime();
    }

    /**
     * 指定日期的0点
     *
     * @param date
     * @return
     * @throws ParseException
     */
    public static Date fetchDateZero(Date date) {
        try {
            return parseDate(formatDate(date, YYYY_MM_DD), YYYY_MM_DD);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 指定日期的0点
     *
     * @param timeStamp
     * @return
     */
    public static Long fetchDateZero4Long(Long timeStamp) {
        Calendar calender = Calendar.getInstance();
        calender.setTimeInMillis(timeStamp);
        calender.set(calender.get(Calendar.YEAR), calender.get(Calendar.MONTH), calender.get(Calendar.DATE), 0, 0, 0);
        return calender.getTimeInMillis();
    }

    /**
     * 时间戳转为整秒
     *
     * @param timeStamp
     * @return
     * @throws ParseException
     */
    public static Long fetchWholeSecond(Long timeStamp) {
        try {
            return timeStamp / 1000 * 1000;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 时间差转n天n小时n分钟n秒n毫秒
     *
     * @param mss
     * @return
     */
    public static String formatDuring(long mss) {
        if (mss == 0) {
            return "0";
        }

        long days = mss / (1000 * 60 * 60 * 24);
        long hours = (mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60);
        long minutes = (mss % (1000 * 60 * 60)) / (1000 * 60);
        long seconds = (mss % (1000 * 60)) / 1000;

        StringBuffer rs = new StringBuffer();
        if (days != 0) {
            rs.append(days + "天");
        }
        if (hours != 0) {
            rs.append(hours + "小时");
        }
        if (minutes != 0) {
            rs.append(minutes + "分钟");
        }
        if (seconds != 0) {
            rs.append(seconds + "秒");
        }
        return rs.toString();
    }


    /**
     * 求当前日期+n天的日期开始时间
     *
     * @param n 要当天要加的数字-数n表示n天前,正数为n天后
     * @return long
     */
    public static long dayBeginTime(int n) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, n);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }

    /**
     * 获取指定时间n小时之前
     *
     * @param n
     * @return
     * @throws ParseException
     */
    public static Long fetchPreHour(Long dateTime, int n) throws ParseException {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date(dateTime));
        cal.add(Calendar.HOUR, n);
        return cal.getTimeInMillis();
    }

    /**
     * 获取指定时间n分钟之前
     *
     * @param monthTime
     * @return
     * @throws ParseException
     */
    public static Long fetchPreMinute(Long monthTime, int n) throws ParseException {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date(monthTime));
        cal.add(Calendar.MINUTE, n);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime().getTime();
    }

    public static Long getTodayBegin() {
        Calendar cal = Calendar.getInstance();
        Calendar todayBegin = new GregorianCalendar(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH), cal.get(Calendar.DAY_OF_MONTH));
        return todayBegin.getTimeInMillis();
    }

    public static String parseTimeToDate(long time, String pattern) {
        Date date = new Date(time);
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(date);
    }

    /**
     * 获取指定时间往后取整0或整5分钟
     *
     * @param monthTime
     * @return
     * @throws ParseException
     */
    public static Long fetchZeroOrFiveMinute(Long monthTime) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date(monthTime));

        // 取得分钟数
        int minute = cal.get(Calendar.MINUTE);
        // 取得分钟数个位数字
        int remainder = minute % 5;
        if (remainder == 1 || remainder == 2 || remainder == 3 || remainder == 4) {
            cal.add(Calendar.MINUTE, 5 - remainder);
        }

        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime().getTime();
    }

    /**
     * 获取指定时间往前取整0或整5分钟
     *
     * @param monthTime
     * @return
     * @throws ParseException
     */
    public static Long fetchZeroOrFiveBeforeMinute(Long monthTime) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date(monthTime));

        // 取得分钟数
        int minute = cal.get(Calendar.MINUTE);
        // 取得分钟数个位数字
        int remainder = minute % 5;
        if (remainder == 1 || remainder == 2 || remainder == 3 || remainder == 4) {
            cal.add(Calendar.MINUTE, -remainder);
        }

        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime().getTime();
    }

    /**
     * 获取传入时间的整N分钟
     * 如：取时间往前最接近的整15分钟(包括0、15、30、45)
     *
     * @param initialTime 初始时间
     * @param n           如5、10、15等
     * @param direction   0：往前取 1：往后取
     * @return
     */
    public static Long fetchCompleteNMinute(Long initialTime, int n, int direction) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date(initialTime));

        // 取得分钟数
        int minute = cal.get(Calendar.MINUTE);
        // 取得分钟数个位数字
        int remainder = minute % n;

        for (int i = 1; i < n; i++) {
            if (remainder == i) {
                Integer t = direction == 0 ? -remainder : n - remainder;
                cal.add(Calendar.MINUTE, t);
                break;
            }
        }

        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime().getTime();
    }

    /**
     * 判断时间是否在标准时间的5分钟时期内
     *
     * @param time         待判断时间
     * @param standardTime 标准时间
     * @return
     */
    public static boolean isInPeriod(Long time, Long standardTime) {
        Long standardTimeAfter5Min = standardTime.longValue() + 5 * 60 * 1000;
        Long periodStart = fetchZeroOrFiveBeforeMinute(standardTime);
        Long periodEnd = fetchZeroOrFiveBeforeMinute(standardTimeAfter5Min);

        if (time.longValue() >= periodStart.longValue() && time.longValue() < periodEnd.longValue()) {
            return true;
        }

        return false;
    }


    /**
     * 功能描述：返回分
     *
     * @param date 日期
     * @return 返回分钟
     */
    public static int getMinute(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MINUTE);
    }

    /**
     * 功能描述：返回日期
     *
     * @param timeStamp 时间戳
     * @return 返回日期
     */
    public static int getDate(Long timeStamp) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(timeStamp);
        return calendar.get(Calendar.DATE);
    }


    /**
     * 判断某时间是否在指定时间区间内
     *
     * @param nowTime   当前时间， 例如03:30:00
     * @param startTime 开始时间，例如 00:00:00
     * @param endTime   结束时间，例如06:00:00
     * @return
     */
    public static boolean isBetweenTimeRange(Date nowTime, Date startTime, Date endTime) throws ParseException {
        if (startTime.getTime() == endTime.getTime()) {
            return false;
        } else if (startTime.getTime() < endTime.getTime()) {
            return nowTime.getTime() >= startTime.getTime() && nowTime.getTime() < endTime.getTime();
        } else {
            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
            Date beforeZero = sdf.parse("23:59:00");
            Date zero = sdf.parse("00:00:00");
            return (nowTime.getTime() >= startTime.getTime() && nowTime.getTime() <= beforeZero.getTime()) || (nowTime.getTime() >= zero.getTime() && nowTime.getTime() < endTime.getTime());
        }
    }

    /**
     * 给时间加上几个小时
     *
     * @param date 当前时间
     * @param hour 需要加的时间
     * @return
     */
    public static String addHour(Date date, int hour) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        if (date == null) {
            return "";
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        // 24小时制
        cal.add(Calendar.HOUR, hour);
        date = cal.getTime();
        cal = null;
        return format.format(date);
    }

    public static Long addDay(Long timeStamp, int day) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(timeStamp);
        cal.add(Calendar.DAY_OF_MONTH, day);
        return cal.getTimeInMillis();
    }

    /**
     * 获取原始时间所在的整点时间
     *
     * @param sourceTime
     * @return
     */
    public static long getWholePoint(long sourceTime) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(sourceTime);
        Calendar nTime = new GregorianCalendar(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH),
                cal.get(Calendar.DAY_OF_MONTH), cal.get(Calendar.HOUR_OF_DAY), 0);
        return nTime.getTimeInMillis();
    }

    /**
     * 获取两个时间区间的整分钟数集合
     * 如，{0,15,30,45}..{0,5,10,15,20,25,30..}等
     *
     * @param startTime 单位毫秒
     * @param endTime
     * @param length
     * @return
     */
    public static List<Long> splitTime(long startTime, long endTime, int length) {
        List<Long> list = Lists.newArrayList();
        if (startTime > endTime) {
            return list;
        }
        long wholeTime = getWholePoint(startTime);

        long t = wholeTime;
        while (t <= endTime) {
            if (t >= startTime && t <= endTime) {
                list.add(t);
            }
            t += length;
        }

        return list;
    }

    /**
     * 按天分割时间段
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static List<Map<String, Long>> splitDate(long startTime, long endTime) {
        List<Map<String, Long>> list = Lists.newArrayList();
        if (startTime > endTime) {
            return list;
        }

        Map<String, Long> tMap;

        Long st = startTime, et = startTime;
        while (et < endTime) {
            tMap = new HashMap<>();
            et = addDay(fetchDateZero4Long(st), 1);
            if (et > endTime) {
                et = endTime;
            }
            tMap.put("st", st);
            tMap.put("et", et - 1000);

            list.add(tMap);

            st = et;
        }

        return list;
    }

}
